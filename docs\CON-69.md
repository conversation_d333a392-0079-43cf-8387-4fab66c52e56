# CON-69: Add structured product metadata into conversational response

## Requirements

Currently, Conversation API response includes products' information in the form of Markdown text.
UI, as an API consumer, would rather have a structured JSON object with product metadata to work on.  

1. The main goal is to return structured metadata for products in the API response  we have the following options: 

A. **Use the existing SNPD integration to get the metadata as a postprocessing step after getting the response from Conversation Agent**:

- search with filter by IDs (SKUs extracted from the response Markdown or taken from the references field)
- multiple PDP calls (in parallel)

- For RAG search inside `RagSearchManager` make call to SNPD and set metadata field. We look into a document type:
  - type: `QuepasaDocument` – that will be policies, no need to fetch or store metadata for them;
  - type: `QuepasaProduct` – that will be products, we need to fetch (store) metadata for them;

- Meta search uses RAG search under te hood, and SNPD results already have metadata field populated.

SNPD response json metadata example: `/docs/examples/SNPD-response-example.json`

B. **Storing metadata JSON in MinIO/Elasticsearch during ingestion and populating it from there during answer post-processing**

incomming product metadata JSON example: `/docs/examples/shoeby-product-aggregated-example.json`

3. Add a new document type `product` for the ingestion pipeline via the /data/v1/products API endpoint.

4. Add a new type: `QuepasaProduct` inherited from `QuepasaDocument` with two new fields `sku` and `metadata` – for product documents and fill in the metadata field with one of the options above.



